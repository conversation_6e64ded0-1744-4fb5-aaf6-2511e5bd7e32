import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { generateText } from "ai";
import { BusinessRegistrationReportSchema } from "../schemas/business-registration-schema";
import type { BusinessRegistrationReport } from "../types/business-registration-types";
import { apiKeyManager } from "./api-key-manager";

export interface OpenRouterResponse {
	text: string;
	parsedData?: BusinessRegistrationReport;
	usage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};
}

export interface OpenRouterError {
	message: string;
	code?: string;
	status?: number;
}

export interface ProcessingOptions {
	model?: string;
	temperature?: number;
	maxTokens?: number;
	topP?: number;
}

const DEFAULT_OPTIONS: Required<ProcessingOptions> = {
	model: "google/gemini-2.5-flash",
	temperature: 0.1,
	maxTokens: 65536, // Maximum supported by Gemini 2.5 Flash
	topP: 0.8,
};

export class OpenRouterClient {
	private static instance: OpenRouterClient;

	private constructor() {}

	public static getInstance(): OpenRouterClient {
		if (!OpenRouterClient.instance) {
			OpenRouterClient.instance = new OpenRouterClient();
		}
		return OpenRouterClient.instance;
	}

	private getOpenRouterProvider() {
		const apiKey = apiKeyManager.getApiKey();
		if (!apiKey) {
			throw new Error("No API key configured. Please set your OpenRouter API key.");
		}

		return createOpenRouter({
			apiKey: apiKey,
		});
	}

	public async processDocument(
		pdfBase64: string,
		prompt: string,
		options: ProcessingOptions = {},
	): Promise<OpenRouterResponse> {
		const config = { ...DEFAULT_OPTIONS, ...options };
		const provider = this.getOpenRouterProvider();

		try {
			console.log("Processing document with Vercel AI SDK and OpenRouter...");
			console.log("Model:", config.model);

			// Convert base64 to Uint8Array for browser compatibility
			const binaryString = atob(pdfBase64);
			const bytes = new Uint8Array(binaryString.length);
			for (let i = 0; i < binaryString.length; i++) {
				bytes[i] = binaryString.charCodeAt(i);
			}
			const pdfBuffer = bytes;

			console.log("Using text generation to handle tool call responses...");

			// Use generateText with messages format and file content
			const { text, usage } = await generateText({
				model: provider(config.model),
				messages: [
					{
						role: 'user',
						content: [
							{
								type: 'text',
								text: prompt,
							},
							{
								type: 'file',
								data: pdfBuffer,
								mimeType: 'application/pdf',
							},
						],
					},
				],
				temperature: config.temperature,
				maxTokens: config.maxTokens,
				topP: config.topP,
			});

			console.log("Response received from OpenRouter/Gemini");
			console.log("Raw text response:", text);

			// Parse the response - it might be a tool call or direct JSON
			let actualObject: unknown;
			try {
				// First, try to parse as direct JSON
				actualObject = JSON.parse(text.trim());
				console.log("Parsed as direct JSON");
			} catch (directParseError) {
				console.log("Not direct JSON, checking for tool call format...");

				// Try to extract from tool call format
				try {
					// Look for JSON in various formats
					let jsonContent = text.trim();

					// Remove markdown code blocks
					if (jsonContent.startsWith("```json")) {
						jsonContent = jsonContent.replace(/^```json\s*/, "").replace(/\s*```$/, "");
					} else if (jsonContent.startsWith("```")) {
						jsonContent = jsonContent.replace(/^```\s*/, "").replace(/\s*```$/, "");
					}

					// Try to parse the cleaned content
					const possibleToolCall = JSON.parse(jsonContent);

					// Check if it's a tool call with input field
					if (possibleToolCall && typeof possibleToolCall === 'object' && 'input' in possibleToolCall) {
						console.log("Found tool call format, extracting JSON from input field...");
						actualObject = JSON.parse(possibleToolCall.input);
						console.log("Successfully extracted JSON from tool call");
					} else {
						actualObject = possibleToolCall;
					}
				} catch (toolCallParseError) {
					console.error("Failed to parse response in any format:", toolCallParseError);
					console.error("Raw response:", text);
					throw new Error("Failed to parse AI response as JSON. The response may not be valid JSON.");
				}
			}

			console.log("Final parsed object:", JSON.stringify(actualObject, null, 2));

			// Object is already validated by generateObject, but let's double-check
			const validationResult = BusinessRegistrationReportSchema.safeParse(actualObject);
			if (!validationResult.success) {
				console.error("Schema validation failed:", validationResult.error);
				console.error("Generated object that failed validation:", JSON.stringify(actualObject, null, 2));

				// Log specific validation errors
				const errorDetails = validationResult.error.issues.map(err => ({
					path: err.path.join('.'),
					message: err.message,
					code: err.code,
					received: 'received' in err ? err.received : undefined
				}));
				console.error("Validation error details:", errorDetails);

				throw new Error(`Schema validation failed: ${validationResult.error.message}. Check console for detailed error information.`);
			}

			const openRouterResponse: OpenRouterResponse = {
				text: JSON.stringify(actualObject, null, 2),
				parsedData: validationResult.data as BusinessRegistrationReport,
				usage: usage
					? {
							promptTokens: usage.promptTokens || 0,
							completionTokens: usage.completionTokens || 0,
							totalTokens: usage.totalTokens || 0,
						}
					: undefined,
			};

			console.log("✅ Structured object generated and validated successfully");
			console.log("Usage:", openRouterResponse.usage);

			return openRouterResponse;
		} catch (error) {
			console.error("OpenRouter/Gemini API error:", error);

			// Log more details about the error
			if (error instanceof Error) {
				console.error("Error details:", {
					name: error.name,
					message: error.message,
					stack: error.stack,
				});

				// Check if it's a schema validation error
				if (error.message.includes("Schema validation failed") ||
					error.message.includes("response did not match schema")) {
					throw new Error("Schema validation error: The AI response did not match the expected format. This may be due to malformed JSON or missing required fields. Please try again.");
				}

				throw new Error(`OpenRouter API error: ${error.message}`);
			}
			throw new Error(`Unexpected error: ${error}`);
		}
	}

	public async testConnection(): Promise<boolean> {
		try {
			const apiKey = apiKeyManager.getApiKey();
			if (!apiKey) {
				throw new Error("No API key configured");
			}

			console.log("Testing OpenRouter API connection with credits endpoint...");

			const response = await fetch("https://openrouter.ai/api/v1/credits", {
				method: "GET",
				headers: {
					"Authorization": `Bearer ${apiKey}`,
					"Content-Type": "application/json",
				},
			});

			if (response.ok) {
				const data = await response.json();
				console.log("✅ OpenRouter connection successful. Credits:", data);
				return true;
			}

			const errorData = await response.json().catch(() => null);
			console.error("❌ OpenRouter connection failed:", errorData);
			return false;
		} catch (error) {
			console.error("Connection test failed:", error);
			return false;
		}
	}
}

export const openRouterClient = OpenRouterClient.getInstance();