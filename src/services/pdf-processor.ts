import CryptoJS from "crypto-js";
import type { BusinessRegistrationReport } from "../types/business-registration-types";
import { openRouterClient } from "./openrouter-client";

export interface ProcessingProgress {
	stage:
		| "uploading"
		| "converting"
		| "analyzing"
		| "parsing"
		| "caching"
		| "completed"
		| "error";
	message: string;
	progress: number; // 0-100
	details?: string;
}

export interface ProcessingResult {
	success: boolean;
	data?: BusinessRegistrationReport;
	error?: string;
	fromCache?: boolean;
	processingTime?: number;
	tokenUsage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};
}

export type ProgressCallback = (progress: ProcessingProgress) => void;

const EXTRACTION_PROMPT = `Extract data from this Macau business registration document and return it in this EXACT JSON structure:

{
  "reportInformation": {
    "reportTitle": {"chinese": "商業登記証明書", "portuguese": "Certidão do Registo Comercial"},
    "registry": {"chinese": "商業及動產登記局", "portuguese": "Conservatória do Registo Comercial e de Bens Móveis"},
    "applicationNumber": "BSA-318-20250728",
    "registrationNumber": "85146 (SO)",
    "companyName": {"chinese": "臻新集團有限公司", "portuguese": "JHEN SIN GRUPO LIMITADA", "english": "JHEN SIN GROUP COMPANY LIMITED"},
    "registeredAddress": {"full": "澳門圓台街無門牌號數晉海地下D座"},
    "informationAsOf": "2025-07-26",
    "printDate": "2025-07-28"
  },
  "inscriptionsAndEndorsements": [
    {
      "entryType": "inscription",
      "registrationNumber": "85146 (SO)",
      "applicationNumber": "AP. 51/31082020",
      "subject": "incorporation",
      "subjectDescription": {"chinese": "設立", "portuguese": "acto constitutivo"},
      "documents": ["設立文件", "公司章程"],
      "registrar": "譚佩雯",
      "date": "2020-08-27",
      "status": "active",
      "companyName": {"chinese": "臻新集團有限公司", "portuguese": "JHEN SIN GRUPO LIMITADA", "english": "JHEN SIN GROUP COMPANY LIMITED"},
      "registeredAddress": {"full": "澳門鏡湖馬路126號信邦華庭4樓A座"},
      "businessObjects": ["飲食零售", "餐飲配送"],
      "capital": {"amount": 100000, "currency": "MOP", "formatted": "MOP $100,000.00"},
      "shareholders": [
        {
          "name": {"full": "蘇梓峰 NATALINO CHAU SOARES", "chinese": "蘇梓峰", "portuguese": "NATALINO CHAU SOARES"},
          "sex": "M",
          "civilStatus": "married",
          "spouse": {"full": "SOARES CHIO U IAN ROWENA", "portuguese": "SOARES CHIO U IAN ROWENA"},
          "propertyRegime": "separation",
          "domicile": {"full": "澳門鏡湖馬路126號信邦華庭4樓A座"},
          "quota": {"amount": 30000, "currency": "MOP", "formatted": "$30,000.00"},
          "percentage": 30
        }
      ],
      "administration": {
        "composition": "股東",
        "administrators": [{"name": {"full": "蘇梓峰 NATALINO CHAU SOARES", "chinese": "蘇梓峰", "portuguese": "NATALINO CHAU SOARES"}, "sex": "M"}],
        "formOfObligation": "壹名行政管理機關成員簽署"
      }
    }
  ],
  "metadata": {
    "totalPages": 15,
    "fees": {"amount": 20.00, "currency": "MOP", "formatted": "MOP $20.00"}
  }
}

CRITICAL: Use this EXACT structure. Replace the example values with actual data from the document. For single people, omit spouse and propertyRegime. Return only valid JSON, no markdown.`;

export class PdfProcessor {
	private static instance: PdfProcessor;

	private constructor() {}

	public static getInstance(): PdfProcessor {
		if (!PdfProcessor.instance) {
			PdfProcessor.instance = new PdfProcessor();
		}
		return PdfProcessor.instance;
	}

	public async processFile(
		file: File,
		onProgress?: ProgressCallback,
	): Promise<ProcessingResult> {
		const startTime = Date.now();

		try {
			// Stage 1: Upload validation
			onProgress?.({
				stage: "uploading",
				message: "Validating PDF file...",
				progress: 10,
			});

			if (file.type !== "application/pdf") {
				throw new Error("Only PDF files are supported");
			}

			if (file.size > 10 * 1024 * 1024) {
				// 10MB limit
				throw new Error("File size must be less than 10MB");
			}

			// Stage 2: Convert to base64
			onProgress?.({
				stage: "converting",
				message: "Converting PDF to base64...",
				progress: 20,
			});

			const base64 = await this.fileToBase64(file);
			const fileHash = this.generateFileHash(base64);

			// Check cache first
			const cached = await this.getCachedResult(fileHash);
			if (cached) {
				onProgress?.({
					stage: "completed",
					message: "Loaded from cache",
					progress: 100,
				});

				return {
					success: true,
					data: cached,
					fromCache: true,
					processingTime: Date.now() - startTime,
				};
			}

			// Stage 3: Analyze with Gemini
			onProgress?.({
				stage: "analyzing",
				message: "Analyzing document with AI...",
				progress: 40,
				details: "This may take 30-60 seconds for complex documents",
			});

			const response = await openRouterClient.processDocument(
				base64,
				EXTRACTION_PROMPT,
			);

			console.log("response: ", response);

			// Stage 4: Parse response
			onProgress?.({
				stage: "parsing",
				message: "Parsing extracted data...",
				progress: 80,
			});

			// Use validated parsed data if available, otherwise fall back to manual parsing
			const parsedData =
				response.parsedData || this.parseOpenRouterResponse(response.text);

			// Stage 5: Cache result
			onProgress?.({
				stage: "caching",
				message: "Saving to cache...",
				progress: 90,
			});

			await this.cacheResult(fileHash, parsedData, file.name);

			onProgress?.({
				stage: "completed",
				message: "Processing completed successfully",
				progress: 100,
			});

			return {
				success: true,
				data: parsedData,
				fromCache: false,
				processingTime: Date.now() - startTime,
				tokenUsage: response.usage,
			};
		} catch (error) {
			onProgress?.({
				stage: "error",
				message:
					error instanceof Error ? error.message : "Unknown error occurred",
				progress: 0,
			});

			return {
				success: false,
				error:
					error instanceof Error ? error.message : "Unknown error occurred",
				processingTime: Date.now() - startTime,
			};
		}
	}

	private async fileToBase64(file: File): Promise<string> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onload = () => {
				const result = reader.result as string;
				// Remove data URL prefix to get pure base64
				const base64 = result.split(",")[1];
				resolve(base64);
			};
			reader.onerror = () => reject(new Error("Failed to read file"));
			reader.readAsDataURL(file);
		});
	}

	private generateFileHash(base64: string): string {
		return CryptoJS.SHA256(base64).toString(CryptoJS.enc.Hex);
	}

	private parseOpenRouterResponse(
		responseText: string,
	): BusinessRegistrationReport {
		try {
			// Clean the response text
			let cleanedResponse = responseText.trim();

			// Remove markdown code blocks if present
			if (cleanedResponse.startsWith("```json")) {
				cleanedResponse = cleanedResponse
					.replace(/^```json\s*/, "")
					.replace(/\s*```$/, "");
			} else if (cleanedResponse.startsWith("```")) {
				cleanedResponse = cleanedResponse
					.replace(/^```\s*/, "")
					.replace(/\s*```$/, "");
			}

			const parsed = JSON.parse(cleanedResponse);

			// Basic validation
			if (!parsed.reportInformation || !parsed.inscriptionsAndEndorsements) {
				throw new Error("Invalid response structure: missing required fields");
			}

			if (!Array.isArray(parsed.inscriptionsAndEndorsements)) {
				throw new Error(
					"Invalid response structure: inscriptionsAndEndorsements must be an array",
				);
			}

			return parsed as BusinessRegistrationReport;
		} catch (error) {
			if (error instanceof SyntaxError) {
				throw new Error(
					`Failed to parse AI response as JSON: ${error.message}`,
				);
			}
			throw error;
		}
	}

	private async getCachedResult(
		fileHash: string,
	): Promise<BusinessRegistrationReport | null> {
		try {
			const { cacheManager } = await import("./cache-manager");
			return await cacheManager.get(fileHash);
		} catch {
			return null;
		}
	}

	private async cacheResult(
		fileHash: string,
		data: BusinessRegistrationReport,
		fileName: string,
	): Promise<void> {
		try {
			const { cacheManager } = await import("./cache-manager");
			await cacheManager.set(fileHash, data, {
				fileName,
				processedAt: new Date().toISOString(),
				size: JSON.stringify(data).length,
			});
		} catch (error) {
			console.warn("Failed to cache result:", error);
		}
	}
}

export const pdfProcessor = PdfProcessor.getInstance();
