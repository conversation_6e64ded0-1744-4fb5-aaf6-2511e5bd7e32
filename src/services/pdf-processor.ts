import CryptoJS from "crypto-js";
import type { BusinessRegistrationReport } from "../types/business-registration-types";
import { openRouterClient } from "./openrouter-client";

export interface ProcessingProgress {
	stage:
		| "uploading"
		| "converting"
		| "analyzing"
		| "parsing"
		| "caching"
		| "completed"
		| "error";
	message: string;
	progress: number; // 0-100
	details?: string;
}

export interface ProcessingResult {
	success: boolean;
	data?: BusinessRegistrationReport;
	error?: string;
	fromCache?: boolean;
	processingTime?: number;
	tokenUsage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};
}

export type ProgressCallback = (progress: ProcessingProgress) => void;

const EXTRACTION_PROMPT = `You are an expert at extracting structured data from Macau business registration documents (商業登記証明書 / Certidão do Registo Comercial).

Your task is to analyze this PDF document and extract ALL information into a precise JSON structure that EXACTLY matches the required schema.

IMPORTANT: Return ONLY the JSON object, no other text, no markdown formatting, no tool calls, no wrapper objects. Just the pure JSON.

CRITICAL: The JSON response MUST have this EXACT structure:

{
  "reportInformation": {
    "reportTitle": {
      "chinese": "商業登記証明書",
      "portuguese": "Certidão do Registo Comercial"
    },
    "registry": {
      "chinese": "商業及動產登記局",
      "portuguese": "Conservatória do Registo Comercial e de Bens Móveis"
    },
    "applicationNumber": "string (e.g., BSA-318-20250728)",
    "registrationNumber": "string (e.g., 85146 (SO))",
    "companyName": {
      "chinese": "string",
      "portuguese": "string",
      "english": "string"
    },
    "registeredAddress": {
      "full": "string (complete address)"
    },
    "informationAsOf": "string (YYYY-MM-DD format)",
    "printDate": "string (YYYY-MM-DD format)"
  },
  "inscriptionsAndEndorsements": [
    {
      "entryType": "inscription" | "endorsement",
      "registrationNumber": "string",
      "applicationNumber": "string",
      "subject": "incorporation" | "quota_acquisition" | "quota_transfer" | "quota_division" | "bylaws_amendment" | "address_change" | "person_name_change" | "company_name_change" | "civil_status_change" | "appointment" | "function_termination" | "cancellation",
      "subjectDescription": {
        "chinese": "string",
        "portuguese": "string"
      },
      "documents": ["string array of document types"],
      "registrar": "string (registrar name)",
      "date": "string (YYYY-MM-DD format)",
      "status": "active" | "cancelled",
      // Additional fields based on subject type...
    }
  ],
  "metadata": {
    "totalPages": number,
    "fees": {
      "amount": number,
      "currency": "MOP" | "HKD" | "USD" | "CNY",
      "formatted": "string"
    }
  }
}

FIELD REQUIREMENTS:
1. All monetary amounts MUST include: amount (number), currency (enum), formatted (string)
2. Person names MUST include: full (required), chinese (optional), portuguese (optional)
3. Sex MUST be: "M" or "F"
4. Civil status MUST be: "single", "married", "divorced", or "widowed"
5. Currency MUST be: "MOP", "HKD", "USD", or "CNY"
6. Entry subjects MUST be exact strings from the list above
7. Dates MUST be in YYYY-MM-DD format
8. All required fields must be present - use empty strings if data is missing

CRITICAL FIELD MAPPINGS FOR ENTRIES:
For quota transfer/acquisition entries, use this EXACT structure:
{
  "entryType": "inscription",
  "subject": "quota_acquisition" | "quota_transfer",
  "subjectDescription": { "chinese": "取得", "portuguese": "aquisição" },
  "activeParties": [
    {
      "name": { "full": "龍志威 LONG CHI WAI", "chinese": "龍志威", "portuguese": "LONG CHI WAI" },
      "sex": "M",
      "civilStatus": "married",
      "spouse": { "full": "鄭健娜 CHEANG KIN NA", "chinese": "鄭健娜", "portuguese": "CHEANG KIN NA" },
      "propertyRegime": "general_community",
      "domicile": { "full": "澳門漁翁街晉海第1座8樓C室" },
      "quota": { "amount": 20000.00, "currency": "MOP", "formatted": "$20,000.00" },
      "percentage": 20.0
    }
  ],
  "passiveParties": [
    {
      "shareholder": { "full": "郭號民 KUOK HOU MAN", "chinese": "郭號民", "portuguese": "KUOK HOU MAN" },
      "quota": { "amount": 20000.00, "currency": "MOP", "formatted": "$20,000.00" }
    }
  ],
  "cause": "股之轉讓",
  "value": { "amount": 57680.00, "currency": "MOP", "formatted": "$57,680.00" }
}

PROPERTY REGIME MAPPING:
- "一般共同財產制" → "general_community"
- "分別財產制" → "separation"
- "共同財產制" → "community"

PERSON STRUCTURE: Always use "name" (not "person"), "domicile" (not "address"), and exact enum values.

The response will be automatically validated against a strict schema, so ensure perfect compliance with the structure above.`;

export class PdfProcessor {
	private static instance: PdfProcessor;

	private constructor() {}

	public static getInstance(): PdfProcessor {
		if (!PdfProcessor.instance) {
			PdfProcessor.instance = new PdfProcessor();
		}
		return PdfProcessor.instance;
	}

	public async processFile(
		file: File,
		onProgress?: ProgressCallback,
	): Promise<ProcessingResult> {
		const startTime = Date.now();

		try {
			// Stage 1: Upload validation
			onProgress?.({
				stage: "uploading",
				message: "Validating PDF file...",
				progress: 10,
			});

			if (file.type !== "application/pdf") {
				throw new Error("Only PDF files are supported");
			}

			if (file.size > 10 * 1024 * 1024) {
				// 10MB limit
				throw new Error("File size must be less than 10MB");
			}

			// Stage 2: Convert to base64
			onProgress?.({
				stage: "converting",
				message: "Converting PDF to base64...",
				progress: 20,
			});

			const base64 = await this.fileToBase64(file);
			const fileHash = this.generateFileHash(base64);

			// Check cache first
			const cached = await this.getCachedResult(fileHash);
			if (cached) {
				onProgress?.({
					stage: "completed",
					message: "Loaded from cache",
					progress: 100,
				});

				return {
					success: true,
					data: cached,
					fromCache: true,
					processingTime: Date.now() - startTime,
				};
			}

			// Stage 3: Analyze with Gemini
			onProgress?.({
				stage: "analyzing",
				message: "Analyzing document with AI...",
				progress: 40,
				details: "This may take 30-60 seconds for complex documents",
			});

			const response = await openRouterClient.processDocument(
				base64,
				EXTRACTION_PROMPT,
			);

			console.log("response: ", response);

			// Stage 4: Parse response
			onProgress?.({
				stage: "parsing",
				message: "Parsing extracted data...",
				progress: 80,
			});

			// Use validated parsed data if available, otherwise fall back to manual parsing
			const parsedData =
				response.parsedData || this.parseOpenRouterResponse(response.text);

			// Stage 5: Cache result
			onProgress?.({
				stage: "caching",
				message: "Saving to cache...",
				progress: 90,
			});

			await this.cacheResult(fileHash, parsedData, file.name);

			onProgress?.({
				stage: "completed",
				message: "Processing completed successfully",
				progress: 100,
			});

			return {
				success: true,
				data: parsedData,
				fromCache: false,
				processingTime: Date.now() - startTime,
				tokenUsage: response.usage,
			};
		} catch (error) {
			onProgress?.({
				stage: "error",
				message:
					error instanceof Error ? error.message : "Unknown error occurred",
				progress: 0,
			});

			return {
				success: false,
				error:
					error instanceof Error ? error.message : "Unknown error occurred",
				processingTime: Date.now() - startTime,
			};
		}
	}

	private async fileToBase64(file: File): Promise<string> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onload = () => {
				const result = reader.result as string;
				// Remove data URL prefix to get pure base64
				const base64 = result.split(",")[1];
				resolve(base64);
			};
			reader.onerror = () => reject(new Error("Failed to read file"));
			reader.readAsDataURL(file);
		});
	}

	private generateFileHash(base64: string): string {
		return CryptoJS.SHA256(base64).toString(CryptoJS.enc.Hex);
	}

	private parseOpenRouterResponse(
		responseText: string,
	): BusinessRegistrationReport {
		try {
			// Clean the response text
			let cleanedResponse = responseText.trim();

			// Remove markdown code blocks if present
			if (cleanedResponse.startsWith("```json")) {
				cleanedResponse = cleanedResponse
					.replace(/^```json\s*/, "")
					.replace(/\s*```$/, "");
			} else if (cleanedResponse.startsWith("```")) {
				cleanedResponse = cleanedResponse
					.replace(/^```\s*/, "")
					.replace(/\s*```$/, "");
			}

			const parsed = JSON.parse(cleanedResponse);

			// Basic validation
			if (!parsed.reportInformation || !parsed.inscriptionsAndEndorsements) {
				throw new Error("Invalid response structure: missing required fields");
			}

			if (!Array.isArray(parsed.inscriptionsAndEndorsements)) {
				throw new Error(
					"Invalid response structure: inscriptionsAndEndorsements must be an array",
				);
			}

			return parsed as BusinessRegistrationReport;
		} catch (error) {
			if (error instanceof SyntaxError) {
				throw new Error(
					`Failed to parse AI response as JSON: ${error.message}`,
				);
			}
			throw error;
		}
	}

	private async getCachedResult(
		fileHash: string,
	): Promise<BusinessRegistrationReport | null> {
		try {
			const { cacheManager } = await import("./cache-manager");
			return await cacheManager.get(fileHash);
		} catch {
			return null;
		}
	}

	private async cacheResult(
		fileHash: string,
		data: BusinessRegistrationReport,
		fileName: string,
	): Promise<void> {
		try {
			const { cacheManager } = await import("./cache-manager");
			await cacheManager.set(fileHash, data, {
				fileName,
				processedAt: new Date().toISOString(),
				size: JSON.stringify(data).length,
			});
		} catch (error) {
			console.warn("Failed to cache result:", error);
		}
	}
}

export const pdfProcessor = PdfProcessor.getInstance();
