import { z } from "zod";

// Core schema types
export const MultilingualNameSchema = z.object({
	chinese: z.string(),
	portuguese: z.string(),
	english: z.string(),
});

export const MonetaryAmountSchema = z.object({
	amount: z.number(),
	currency: z.enum(["MOP", "HKD", "USD", "CNY"]),
	formatted: z.string(),
});

export const AddressSchema = z.object({
	full: z.string(),
});

export const PersonNameSchema = z.object({
	chinese: z.string().optional(),
	portuguese: z.string().optional(),
	full: z.string(),
});

// Person information schemas
export const PersonInfoSchema = z.object({
	name: PersonNameSchema,
	sex: z.enum(["M", "F"]),
	civilStatus: z.enum(["single", "married", "divorced", "widowed"]),
	spouse: PersonNameSchema.optional(),
	propertyRegime: z
		.enum(["separation", "community", "general_community"])
		.optional(),
	domicile: AddressSchema,
});

export const ShareholderInfoSchema = PersonInfoSchema.extend({
	quota: MonetaryAmountSchema,
	percentage: z.number().optional(),
	// Additional fields for UI components
	name_zh: z.string().optional(),
	name_pt: z.string().optional(),
	current_stake: z.number().nullable().optional(),
	gender: z.string().nullable().optional(),
	marital_status: z.string().nullable().optional(),
	spouse_zh: z.string().nullable().optional(),
	regime: z.string().nullable().optional(),
	property_regime: z.string().optional(),
	address: z.string().nullable().optional(),
	currency: z.string().optional(),
	is_active: z.boolean().nullable().optional(),
	has_no_data: z.boolean().optional(),
	historical_stake: z.number().optional(),
});

export const AdministratorInfoSchema = z.object({
	name: PersonNameSchema,
	sex: z.enum(["M", "F"]).optional(),
	civilStatus: z.string().optional(),
	domicile: AddressSchema.optional(),
});

export const AdministrationSchema = z.object({
	composition: z.string().optional(),
	administrators: z.array(AdministratorInfoSchema),
	formOfObligation: z.string(),
});

// Entry type schemas
export const EntryTypeSchema = z.enum(["inscription", "endorsement"]);

export const EntrySubjectSchema = z.enum([
	"incorporation",
	"address_change",
	"function_termination",
	"person_name_change",
	"company_name_change",
	"civil_status_change",
	"quota_acquisition",
	"quota_transfer",
	"quota_unification",
	"quota_division",
	"appointment",
	"cancellation",
	"bylaws_amendment",
]);

export const BaseRegistrationEntrySchema = z.object({
	entryType: EntryTypeSchema,
	registrationNumber: z.string().optional(),
	applicationNumber: z.string(),
	subject: EntrySubjectSchema,
	subjectDescription: z.object({
		chinese: z.string(),
		portuguese: z.string(),
	}),
	documents: z.array(z.string()),
	registrar: z.string(),
	date: z.string().optional(),
	status: z.enum(["active", "cancelled"]).optional(),
	// Additional fields for processed data
	event_id: z.number().optional(),
	type: z.string().optional(),
	type_pt: z.string().optional(),
	entry_type: z.string().optional(),
	details: z.record(z.string(), z.unknown()).optional(),
});

// Specific entry schemas
export const IncorporationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("incorporation"),
	companyName: MultilingualNameSchema,
	registeredAddress: AddressSchema,
	businessObjects: z.array(z.string()),
	capital: MonetaryAmountSchema,
	shareholders: z.array(ShareholderInfoSchema),
	administration: AdministrationSchema,
	clause: z.string().optional(),
});

export const QuotaTransferEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.enum(["quota_acquisition", "quota_transfer"]),
	cause: z.string(),
	activeParties: z.array(ShareholderInfoSchema),
	passiveParties: z.array(
		z.object({
			shareholder: PersonNameSchema,
			quota: MonetaryAmountSchema.optional(),
		}),
	),
	value: z.union([MonetaryAmountSchema, z.string()]),
	dividedQuota: MonetaryAmountSchema.optional(),
	reservedQuota: MonetaryAmountSchema.optional(),
	cededQuotas: MonetaryAmountSchema.optional(),
});

export const QuotaUnificationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("quota_unification"),
	titleHolder: ShareholderInfoSchema,
	unifiedQuota: MonetaryAmountSchema,
	originalQuotas: z.array(MonetaryAmountSchema).optional(),
	unificationDetails: z.string().optional(),
});

export const AddressChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("address_change"),
	newAddress: AddressSchema,
});

export const PersonNameChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("person_name_change"),
	details: z.record(z.string(), z.unknown()).optional(),
	person: PersonNameSchema,
	oldName: PersonNameSchema.optional(),
	newName: PersonNameSchema.optional(),
});

export const CompanyNameChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("company_name_change"),
	details: z.record(z.string(), z.unknown()).optional(),
	newCompanyName: MultilingualNameSchema,
});

export const CivilStatusChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("civil_status_change"),
	details: z.record(z.string(), z.unknown()).optional(),
	person: PersonNameSchema,
	oldStatus: z.string().optional(),
	newStatus: z.string().optional(),
});

export const AppointmentEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("appointment"),
	administration: AdministrationSchema,
});

export const FunctionTerminationEntrySchema =
	BaseRegistrationEntrySchema.extend({
		subject: z.literal("function_termination"),
		dismissed: z.string().optional(),
		renunciant: z.string().optional(),
	});

export const BylawsAmendmentEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("bylaws_amendment"),
	alterations: z.object({
		capital: MonetaryAmountSchema.optional(),
		shareholders: z.array(ShareholderInfoSchema).optional(),
		administration: AdministrationSchema.optional(),
		formOfObligation: z.string().optional(),
		clause: z.string().optional(),
	}),
});

export const CancellationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("cancellation"),
	cause: z.string(),
	annotatedInscription: z.string().optional(),
	cancelled: z.string().optional(),
});

// Union schema for all registration entries
export const RegistrationEntrySchema = z.discriminatedUnion("subject", [
	IncorporationEntrySchema,
	QuotaTransferEntrySchema,
	QuotaUnificationEntrySchema,
	AddressChangeEntrySchema,
	PersonNameChangeEntrySchema,
	CompanyNameChangeEntrySchema,
	CivilStatusChangeEntrySchema,
	AppointmentEntrySchema,
	FunctionTerminationEntrySchema,
	BylawsAmendmentEntrySchema,
	CancellationEntrySchema,
]);

// Main document schema
export const BusinessRegistrationReportSchema = z.object({
	reportInformation: z.object({
		reportTitle: z.object({
			chinese: z.string(),
			portuguese: z.string(),
		}),
		registry: z.object({
			chinese: z.string(),
			portuguese: z.string(),
		}),
		applicationNumber: z.string(),
		registrationNumber: z.string(),
		companyName: MultilingualNameSchema,
		registeredAddress: AddressSchema,
		informationAsOf: z.string(),
		printDate: z.string(),
	}),
	inscriptionsAndEndorsements: z.array(RegistrationEntrySchema),
	metadata: z
		.object({
			totalPages: z.number().optional(),
			documentId: z.string().optional(),
			platformId: z.string().optional(),
			fees: MonetaryAmountSchema.optional(),
		})
		.optional(),
});

// Utility schemas
export const ParsedDateSchema = z.object({
	original: z.string(),
	parsed: z.date().nullable(),
	format: z.string(),
});

export const ValidationErrorSchema = z.object({
	field: z.string(),
	message: z.string(),
	severity: z.enum(["error", "warning", "info"]),
});

export const ParseResultSchema = z.object({
	data: z.unknown(),
	errors: z.array(ValidationErrorSchema),
	warnings: z.array(ValidationErrorSchema),
});

// Export types inferred from schemas
export type BusinessRegistrationReportZod = z.infer<
	typeof BusinessRegistrationReportSchema
>;
export type RegistrationEntryZod = z.infer<typeof RegistrationEntrySchema>;
export type ShareholderInfoZod = z.infer<typeof ShareholderInfoSchema>;
export type MonetaryAmountZod = z.infer<typeof MonetaryAmountSchema>;
