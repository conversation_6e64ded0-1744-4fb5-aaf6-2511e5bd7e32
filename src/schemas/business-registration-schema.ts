import { z } from "zod";

// Core schema types
export const MultilingualNameSchema = z.object({
	chinese: z.string().describe("Company name in Chinese characters (e.g., '臻新集團有限公司')"),
	portuguese: z.string().describe("Company name in Portuguese (e.g., 'JHEN SIN GRUPO LIMITADA')"),
	english: z.string().describe("Company name in English (e.g., 'JHEN SIN GROUP COMPANY LIMITED')"),
});

export const MonetaryAmountSchema = z.object({
	amount: z.number().describe("Numerical value of the monetary amount (e.g., 100000 for MOP 100,000)"),
	currency: z.enum(["MOP", "HKD", "USD", "CNY"]).describe("Currency code - typically MOP (Macau Pataca) for local companies"),
	formatted: z.string().describe("Human-readable formatted amount (e.g., 'MOP $100,000.00', '$30,000.00', '$57,680.00')"),
});

export const AddressSchema = z.object({
	full: z.string().describe("Complete Macau address in Chinese/Portuguese (e.g., '澳門圓台街無門牌號數晉海地下D座', '澳門鏡湖馬路126號信邦華庭4樓A座')"),
});

export const PersonNameSchema = z.object({
	chinese: z.string().optional().describe("Person's name in Chinese characters (e.g., '蘇梓峰', '馮偉成')"),
	portuguese: z.string().optional().describe("Person's name in Portuguese (e.g., 'NATALINO CHAU SOARES', 'FONG WAI SENG')"),
	full: z.string().describe("Complete name combining Chinese and Portuguese (e.g., '蘇梓峰 NATALINO CHAU SOARES', '馮偉成 FONG WAI SENG')"),
});

// Person information schemas
export const PersonInfoSchema = z.object({
	name: PersonNameSchema.describe("Person's full name in Chinese and Portuguese"),
	sex: z.enum(["M", "F"]).describe("Gender - M for Male, F for Female"),
	civilStatus: z.enum(["single", "married", "divorced", "widowed"]).describe("Marital status - single, married, divorced, or widowed"),
	spouse: PersonNameSchema.optional().describe("Spouse's name if married (e.g., 'SOARES CHIO U IAN ROWENA', '施秋量')"),
	propertyRegime: z
		.enum(["separation", "community", "general_community"])
		.or(z.literal(""))
		.optional()
		.describe("Property regime for married couples - 'separation' (分別財產制), 'community' (共同財產制), 'general_community' (一般共同財產制)"),
	domicile: AddressSchema.describe("Person's residential address in Macau"),
});

export const ShareholderInfoSchema = PersonInfoSchema.extend({
	quota: MonetaryAmountSchema.describe("Shareholder's ownership quota amount (e.g., MOP $30,000.00, $20,000.00, $49,000.00)"),
	percentage: z.number().optional().describe("Ownership percentage as integer (e.g., 30 for 30%, 20 for 20%, 49 for 49%)"),
	// Additional fields for UI components
	name_zh: z.string().optional().describe("Shareholder's name in Chinese (alternative field for UI)"),
	name_pt: z.string().optional().describe("Shareholder's name in Portuguese (alternative field for UI)"),
	current_stake: z.number().nullable().optional().describe("Current ownership percentage as decimal (0-1, e.g., 0.30 for 30%)"),
	gender: z.string().nullable().optional().describe("Gender as string (alternative to sex field)"),
	marital_status: z.string().nullable().optional().describe("Marital status as string (alternative to civilStatus field)"),
	spouse_zh: z.string().nullable().optional().describe("Spouse's name in Chinese"),
	regime: z.string().nullable().optional().describe("Property regime as string (alternative to propertyRegime field)"),
	property_regime: z.string().optional().describe("Property regime description"),
	address: z.string().nullable().optional().describe("Address as string (alternative to domicile field)"),
	currency: z.string().optional().describe("Currency code for the quota (typically 'MOP')"),
	is_active: z.boolean().nullable().optional().describe("Whether the shareholder is currently active"),
	has_no_data: z.boolean().optional().describe("Flag indicating if shareholder data is missing or incomplete"),
	historical_stake: z.number().optional().describe("Previous ownership percentage for historical tracking"),
});

export const AdministratorInfoSchema = z.object({
	name: PersonNameSchema.describe("Administrator's full name in multiple languages"),
	sex: z.enum(["M", "F"]).optional().describe("Administrator's gender - M for Male, F for Female"),
	civilStatus: z.string().optional().describe("Administrator's marital status"),
	domicile: AddressSchema.optional().describe("Administrator's residential address"),
});

export const AdministrationSchema = z.object({
	composition: z.string().optional().describe("Administrative structure description (e.g., '壹名或多名成員, 股東或非股東')"),
	administrators: z.array(AdministratorInfoSchema).describe("List of company administrators/directors"),
	formOfObligation: z.string().describe("How company is legally bound (e.g., '兩名行政管理機關成員聯合簽署', '任一名行政管理機關成員簽署', '壹名行政管理機關成員簽名')"),
});

// Entry type schemas
export const EntryTypeSchema = z.enum(["inscription", "endorsement"]).describe("Type of registry entry - inscription (original registration) or endorsement (modification/annotation)");

export const EntrySubjectSchema = z.enum([
	"incorporation",
	"address_change",
	"function_termination",
	"person_name_change",
	"company_name_change",
	"civil_status_change",
	"quota_acquisition",
	"quota_transfer",
	"quota_unification",
	"quota_division",
	"appointment",
	"cancellation",
	"bylaws_amendment",
]).describe("Subject/purpose of the registry entry - what type of business change or event is being recorded");

export const BaseRegistrationEntrySchema = z.object({
	entryType: EntryTypeSchema.describe("'inscription' for original registrations, 'endorsement' for modifications/annotations"),
	registrationNumber: z.string().optional().describe("Company registration number (e.g., '85146 (SO)')"),
	applicationNumber: z.string().describe("Application number (e.g., 'AP. 51/31082020', 'Of. 20052021', 'BSA-318-20250728')"),
	subject: EntrySubjectSchema.describe("Type of business event (incorporation, quota_transfer, address_change, etc.)"),
	subjectDescription: z.object({
		chinese: z.string().describe("Entry description in Chinese (e.g., '設立', '取得', '法人住所之變更')"),
		portuguese: z.string().describe("Entry description in Portuguese (e.g., 'acto constitutivo', 'aquisição', 'mudança de sede')"),
	}).describe("Bilingual description of the registry entry purpose"),
	documents: z.array(z.string()).describe("Supporting documents (e.g., '設立文件 27-08-2020', '公司章程', '議事錄 05-10-2020', '合同 22-10-2020')"),
	registrar: z.string().describe("Registrar name (e.g., '譚佩雯 (Tam Pui Man)', '梁采怡 (Liang Tsai I)')"),
	date: z.string().optional().describe("Registration date (ISO format YYYY-MM-DD)"),
	status: z.enum(["active", "cancelled"]).optional().describe("Entry status - 'active' for current entries, 'cancelled' for revoked entries"),
	// Additional fields for processed data
	event_id: z.number().optional().describe("Internal event ID for tracking"),
	type: z.string().optional().describe("Entry type as string (alternative field)"),
	type_pt: z.string().optional().describe("Entry type in Portuguese"),
	entry_type: z.string().optional().describe("Entry type description"),
	details: z.record(z.string(), z.unknown()).optional().describe("Additional details specific to this entry type"),
});

// Specific entry schemas
export const IncorporationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("incorporation").describe("Company incorporation/formation entry"),
	companyName: MultilingualNameSchema.describe("Official company name in Chinese, Portuguese, and English"),
	registeredAddress: AddressSchema.describe("Company's official registered address"),
	businessObjects: z.array(z.string()).describe("Business activities (e.g., ['飲食零售', '餐飲配送'])"),
	capital: MonetaryAmountSchema.describe("Initial registered capital (e.g., MOP $100,000.00)"),
	shareholders: z.array(ShareholderInfoSchema).describe("Initial shareholders with their quotas and personal details"),
	administration: AdministrationSchema.describe("Company administration structure and appointed administrators"),
	clause: z.string().optional().describe("Special clauses (e.g., '股轉讓給第三人時,沒有參與股之轉讓之股東享有優先權')"),
});

export const QuotaTransferEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.enum(["quota_acquisition", "quota_transfer"]).describe("'quota_acquisition' or 'quota_transfer' for share transactions"),
	cause: z.string().describe("Reason for transfer (e.g., '股之分割轉讓及部分保留', '股之轉讓', '股之分割及轉讓')"),
	activeParties: z.array(ShareholderInfoSchema).describe("Shareholders acquiring quotas (buyers/recipients)"),
	passiveParties: z.array(
		z.object({
			shareholder: PersonNameSchema.describe("Shareholder transferring their quota (seller)"),
			quota: MonetaryAmountSchema.optional().describe("Specific quota amount being transferred"),
		}),
	).describe("Shareholders transferring quotas (sellers)"),
	value: z.union([MonetaryAmountSchema, z.string()]).describe("Transaction value (e.g., '與票面價值同', '$57,680.00', '$31,724.00; $141,316.00')"),
	dividedQuota: MonetaryAmountSchema.optional().describe("Original quota that was divided (e.g., $30,000.00)"),
	reservedQuota: MonetaryAmountSchema.optional().describe("Portion of quota reserved by original owner (e.g., $15,000.00)"),
	cededQuotas: MonetaryAmountSchema.optional().describe("Portion of quota transferred to new owner (e.g., $15,000.00)"),
});

export const QuotaUnificationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("quota_unification").describe("Unification of multiple quotas into a single quota"),
	titleHolder: ShareholderInfoSchema.describe("Shareholder who holds the unified quota"),
	unifiedQuota: MonetaryAmountSchema.describe("Final unified quota amount (e.g., $35,000.00, $50,000.00)"),
	originalQuotas: z.array(MonetaryAmountSchema).optional().describe("Original quotas being unified (e.g., [$15,000.00, $20,000.00])"),
	unificationDetails: z.string().optional().describe("Unification description (e.g., '合併兩股 após unificação de duas quotas $15,000.00; $20,000.00')"),
});

export const AddressChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("address_change").describe("Change in company's registered address"),
	newAddress: AddressSchema.describe("New registered address (e.g., '澳門圓台街無門牌號數晉海地下D座')"),
});

export const PersonNameChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("person_name_change").describe("Change in person's name (shareholder, spouse, administrator)"),
	details: z.record(z.string(), z.unknown()).optional().describe("Detailed description of the name change"),
	person: PersonNameSchema.describe("Person whose name is changing"),
	oldName: PersonNameSchema.optional().describe("Previous name (e.g., '蘇趙余茵 SOARES CHIO U IAN ROWENA')"),
	newName: PersonNameSchema.optional().describe("New name (e.g., '趙余茵 CHIO U IAN ROWENA')"),
});

export const CompanyNameChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("company_name_change").describe("Correction or change in company's official name"),
	details: z.record(z.string(), z.unknown()).optional().describe("Details about the company name change"),
	newCompanyName: MultilingualNameSchema.describe("Corrected company name in all languages"),
});

export const CivilStatusChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("civil_status_change").describe("Change in person's marital status"),
	details: z.record(z.string(), z.unknown()).optional().describe("Detailed description of civil status change"),
	person: PersonNameSchema.describe("Person whose marital status is changing"),
	oldStatus: z.string().optional().describe("Previous marital status (e.g., 'married')"),
	newStatus: z.string().optional().describe("New marital status (e.g., 'divorced')"),
});

export const AppointmentEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("appointment").describe("Appointment of new administrators or directors"),
	administration: AdministrationSchema.describe("New administration structure and appointed administrators"),
});

export const FunctionTerminationEntrySchema =
	BaseRegistrationEntrySchema.extend({
		subject: z.literal("function_termination").describe("Termination of administrator's or director's function"),
		dismissed: z.string().optional().describe("Person dismissed (e.g., '所有行政管理機關成員', '趙余茵 CHIO U IAN ROWENA')"),
		renunciant: z.string().optional().describe("Person who resigned (e.g., '馮偉成 FONG WAI SENG')"),
	});

export const BylawsAmendmentEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("bylaws_amendment").describe("Amendments to company's bylaws/articles of association"),
	alterations: z.object({
		capital: MonetaryAmountSchema.optional().describe("Updated capital amount (typically remains MOP $100,000.00)"),
		shareholders: z.array(ShareholderInfoSchema).optional().describe("Updated shareholder list with new quotas and percentages"),
		administration: AdministrationSchema.optional().describe("Updated administration structure"),
		formOfObligation: z.string().optional().describe("Updated signing authority (e.g., '壹名行政管理機關成員簽名')"),
		clause: z.string().optional().describe("Updated special clauses (e.g., '股轉讓與非股東須經公司同意, 公司有第一優先權, 股東則次之')"),
	}).describe("Specific changes made to the company's bylaws"),
});

export const CancellationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("cancellation").describe("Cancellation of a previous registration entry"),
	cause: z.string().describe("Reason for cancellation (e.g., '職務之終止')"),
	annotatedInscription: z.string().optional().describe("Reference to cancelled entry (e.g., 'Ap.141/28102020', 'Ap.73/20092021')"),
	cancelled: z.string().optional().describe("What was cancelled (e.g., '所有行政管理機關成員')"),
});

// Union schema for all registration entries - using regular union for more flexibility
export const RegistrationEntrySchema = z.union([
	IncorporationEntrySchema,
	QuotaTransferEntrySchema,
	QuotaUnificationEntrySchema,
	AddressChangeEntrySchema,
	PersonNameChangeEntrySchema,
	CompanyNameChangeEntrySchema,
	CivilStatusChangeEntrySchema,
	AppointmentEntrySchema,
	FunctionTerminationEntrySchema,
	BylawsAmendmentEntrySchema,
	CancellationEntrySchema,
	// Fallback to base schema for unknown entry types
	BaseRegistrationEntrySchema,
]);

// Main document schema
export const BusinessRegistrationReportSchema = z.object({
	reportInformation: z.object({
		reportTitle: z.object({
			chinese: z.string().describe("Report title in Chinese (e.g., '商業登記書面報告')"),
			portuguese: z.string().describe("Report title in Portuguese (e.g., 'Informação Escrita de Registo Comercial')"),
		}).describe("Bilingual title of the business registration report"),
		registry: z.object({
			chinese: z.string().describe("Registry name in Chinese (e.g., '商業及動產登記局')"),
			portuguese: z.string().describe("Registry name in Portuguese (e.g., 'Conservatória dos Registos Comercial e de Bens Móveis')"),
		}).describe("Name of the Macau registry office that issued this report"),
		applicationNumber: z.string().describe("Report application number (e.g., 'BSA-318-20250728')"),
		registrationNumber: z.string().describe("Company registration number (e.g., '85146 (SO)')"),
		companyName: MultilingualNameSchema.describe("Current official company name in all languages"),
		registeredAddress: AddressSchema.describe("Current registered address of the company"),
		informationAsOf: z.string().describe("Date as of which information is current (YYYY-MM-DD format, e.g., '2025-07-26')"),
		printDate: z.string().describe("Date when report was generated (YYYY-MM-DD format, e.g., '2025-07-28')"),
	}).describe("Header information about the business registration report"),
	inscriptionsAndEndorsements: z.array(RegistrationEntrySchema).describe("Chronological list of all registration entries for this company"),
	metadata: z
		.object({
			totalPages: z.number().optional().describe("Total pages in original document (e.g., 15)"),
			documentId: z.string().optional().describe("Document identifier (e.g., 'BSA-318-20250728-528671')"),
			platformId: z.string().optional().describe("Platform identifier (e.g., 'BSA-ET-2025-40444')"),
			fees: MonetaryAmountSchema.optional().describe("Report fees (e.g., MOP $20.00)"),
		})
		.optional()
		.describe("Additional metadata about the document"),
});

// Utility schemas
export const ParsedDateSchema = z.object({
	original: z.string().describe("Original date string as it appeared in the source document"),
	parsed: z.date().nullable().describe("Parsed date object, null if parsing failed"),
	format: z.string().describe("Detected or expected date format (e.g., 'DD/MM/YYYY', 'YYYY-MM-DD')"),
});

export const ValidationErrorSchema = z.object({
	field: z.string().describe("Name of the field that has the validation issue"),
	message: z.string().describe("Human-readable description of the validation issue"),
	severity: z.enum(["error", "warning", "info"]).describe("Severity level - error (blocks processing), warning (potential issue), info (informational)"),
});

export const ParseResultSchema = z.object({
	data: z.unknown().describe("The parsed data object"),
	errors: z.array(ValidationErrorSchema).describe("List of validation errors that occurred during parsing"),
	warnings: z.array(ValidationErrorSchema).describe("List of validation warnings that occurred during parsing"),
});

// Export types inferred from schemas
export type BusinessRegistrationReportZod = z.infer<
	typeof BusinessRegistrationReportSchema
>;
export type RegistrationEntryZod = z.infer<typeof RegistrationEntrySchema>;
export type ShareholderInfoZod = z.infer<typeof ShareholderInfoSchema>;
export type MonetaryAmountZod = z.infer<typeof MonetaryAmountSchema>;
